apply plugin: 'maven-publish'
group 'com.nsy.api.wms' + snapshotGroupEnd
version '1.0.0-SNAPSHOT'

jar.enabled = true

dependencies {
    implementation('org.springframework.cloud:spring-cloud-starter-openfeign')
//    需要注意的是引入openfeign，必须要引入loadbalancer，否则无法启动。
    implementation('org.springframework.cloud:spring-cloud-starter-loadbalancer')
    implementation('org.apache.commons:commons-lang3:3.6')
    implementation('org.codehaus.jackson:jackson-core-asl:1.8.3')
    implementation('javax.inject:javax.inject:1')
    implementation('javax.validation:validation-api:2.0.1.Final')
    implementation('com.alibaba:fastjson:1.2.68')

    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.7.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.7.0'
    implementation('com.google.code.gson:gson:2.7')
}
jar.enabled = true

publishing {
    publications {
        mavenJava(MavenPublication) {
            // bootJar is the default build task configured by Spring Boot
            artifact jar
        }
    }

    repositories {
        maven {
            credentials {
                username getRepositorySnapshotUsername()
                password getRepositorySnapshotPassword()
            }
            url getRepositorySnapshotUrl()
        }
    }
}