package com.nsy.api.wms.response.stockin;

import com.nsy.api.wms.constants.StringConstant;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@ApiModel("PDA退货任务明细列表")
public class StockinReturnProductPDATaskItemResponse {

    @ApiModelProperty("唯一标识")
    private List<Integer> taskItemIdList;

    @ApiModelProperty(value = "唯一标识", hidden = true)
    private String taskItemIds;

    /**
     * 商品sku编码
     */
    @ApiModelProperty("商品sku编码")
    private String sku;

    /**
     * 颜色
     */
    @ApiModelProperty("颜色")
    private String color;

    /**
     * 尺码
     */
    @ApiModelProperty("尺码")
    private String size;

    /**
     * 条形码
     */
    @ApiModelProperty("条形码")
    private String barcode;

    /**
     * sku原图地址
     */
    @ApiModelProperty("sku原图地址")
    private String imageUrl;

    /**
     * 缩略图
     */
    @ApiModelProperty(value = "缩略图地址", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    /**
     * 预览图地址
     */
    @ApiModelProperty(value = "预览图地址", name = "previewImageUrl")
    private String previewImageUrl;

    /**
     * already_generate: 已生成,returning: 退货中,return_success: 退货完成
     */
    @ApiModelProperty("already_generate: 已生成,returning: 退货中,return_success: 退货完成")
    private String status;

    /**
     * 库位编码
     */
    @ApiModelProperty("库位编码")
    private String positionCode;

    /**
     * 待退货数
     */
    @ApiModelProperty("待退货数")
    private Integer waitReturnQty;
    /**
     * 真实退货数（扫描数）
     */
    @ApiModelProperty("真实退货数（扫描数）")
    private Integer actualReturnQty;

    /**
     * 缺货数
     */
    @ApiModelProperty("缺货数")
    private Integer lackProductQty;

    public String getTaskItemIds() {
        return taskItemIds;
    }

    public void setTaskItemIds(String taskItemIds) {
        this.taskItemIds = taskItemIds;
        setTaskItemIdList(StringUtils.hasText(taskItemIds) ? Arrays.stream(taskItemIds.split(StringConstant.COMMA)).map(Integer::parseInt).collect(Collectors.toList()) : Lists.newArrayList());
    }

    public List<Integer> getTaskItemIdList() {
        return taskItemIdList;
    }

    public void setTaskItemIdList(List<Integer> taskItemIdList) {
        this.taskItemIdList = taskItemIdList;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getThumbnailImageUrl() {
        return thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getWaitReturnQty() {
        return waitReturnQty;
    }

    public void setWaitReturnQty(Integer waitReturnQty) {
        this.waitReturnQty = waitReturnQty;
    }

    public Integer getActualReturnQty() {
        return actualReturnQty;
    }

    public void setActualReturnQty(Integer actualReturnQty) {
        this.actualReturnQty = actualReturnQty;
    }

    public Integer getLackProductQty() {
        return lackProductQty;
    }

    public void setLackProductQty(Integer lackProductQty) {
        this.lackProductQty = lackProductQty;
    }
}
