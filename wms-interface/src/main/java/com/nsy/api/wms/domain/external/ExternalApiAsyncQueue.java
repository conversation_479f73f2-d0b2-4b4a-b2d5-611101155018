package com.nsy.api.wms.domain.external;

import io.swagger.annotations.ApiModelProperty;

public class ExternalApiAsyncQueue {

    @ApiModelProperty(value = "id", name = "id")
    private Integer id;

    private String location;

    @ApiModelProperty(value = "apiLogId", name = "apiLogId")
    private Integer apiLogId;

    @ApiModelProperty(value = "单据号", name = "groupNo")
    private String groupNo;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getApiLogId() {
        return apiLogId;
    }

    public void setApiLogId(Integer apiLogId) {
        this.apiLogId = apiLogId;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
