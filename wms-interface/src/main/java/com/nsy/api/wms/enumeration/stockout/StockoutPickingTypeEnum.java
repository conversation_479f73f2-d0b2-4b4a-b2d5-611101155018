package com.nsy.api.wms.enumeration.stockout;

import java.util.Arrays;

public enum StockoutPickingTypeEnum {
    FIND_DOC_BY_GOODS("以货找单"),
    FIND_GOODS_BY_DOC("以单找货"),
    SECOND_SORT("二次分拣"),
    WHOLE_PICK("整单拣货");
    String stockoutPickingType;

    StockoutPickingTypeEnum(String stockoutPickingType) {
        this.stockoutPickingType = stockoutPickingType;
    }

    public String getStockoutPickingType() {
        return stockoutPickingType;
    }

    public static String getNameBy(String pickingType) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(pickingType))
                .findAny()
                .map(StockoutPickingTypeEnum::getStockoutPickingType)
                .orElse(WHOLE_PICK.getStockoutPickingType());
    }
}
