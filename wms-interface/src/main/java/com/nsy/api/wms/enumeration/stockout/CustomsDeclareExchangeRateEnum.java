package com.nsy.api.wms.enumeration.stockout;

import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;
import java.util.Objects;

public enum CustomsDeclareExchangeRateEnum {
    DECLARE_EXCHANGE_RATE("报关汇率"),
    INPUT_RATE("进项比例"),
    FORM_EXCHANGE_RATE("关单汇率");

    String name;

    CustomsDeclareExchangeRateEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String of(String name) {
        if (Strings.isBlank(name)) return null;
        CustomsDeclareExchangeRateEnum resultEnum = Arrays.stream(values())
                .filter(instance -> name.equals(instance.name()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(resultEnum)) return null;

        return resultEnum.getName();
    }

}
