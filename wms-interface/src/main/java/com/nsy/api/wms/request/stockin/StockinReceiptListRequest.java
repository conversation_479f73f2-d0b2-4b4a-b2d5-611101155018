package com.nsy.api.wms.request.stockin;

import com.nsy.api.wms.request.stock.ProductCodePrefixPageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@ApiModel(value = "StockinReceiptListRequest", description = "入库收货列表查询request")
public class StockinReceiptListRequest extends ProductCodePrefixPageRequest {
    @ApiModelProperty("出库单号")
    private String supplierDeliveryNo;

    @ApiModelProperty("供应商Id")
    private Integer supplierId;

    @ApiModelProperty("发货日期-开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDateStart;

    @ApiModelProperty("发货日期-结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDateEnd;

    @ApiModelProperty("仓库Id")
    private Integer spaceId;

    @ApiModelProperty("区域Id")
    private List<Integer> areaIdList;

    @ApiModelProperty("规格编码")
    private String sku;

    @ApiModelProperty("采购单号")
    private String purchaseNo;

    @ApiModelProperty("是否差异:1是0否")
    private Integer difference;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("工厂确认状态")
    private String deliveryConfirmStatus;

    @ApiModelProperty("状态集合")
    private List<String> statusList;

    @ApiModelProperty("出库箱码")
    private String supplierDeliveryBoxCode;

    @ApiModelProperty(value = "采购员userName", name = "purchaseUserName", hidden = true)
    private String purchaseUserName;

    @ApiModelProperty(value = "采购员userId", name = "purchaseUserId")
    private Integer purchaseUserId;

    @ApiModelProperty(value = "跟单员Id", name = "modelMerchandiserEmpId")
    private Integer modelMerchandiserEmpId;

    @ApiModelProperty(value = "跟单员", name = "modelMerchandiserEmpName")
    private String modelMerchandiserEmpName;

    @ApiModelProperty("商品编码")
    private String spu;

    @ApiModelProperty(value = "上架完成时间 - 开始", name = "completeShelvedStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeShelvedStartDate;

    @ApiModelProperty(value = "上架完成时间 - 结束", name = "completeShelvedEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeShelvedEndDate;

    @ApiModelProperty(value = "地区", name = "location")
    private String location;

    @ApiModelProperty(value = "是否快进快出", name = "isFbaQuick")
    private Integer isFbaQuick;

    @ApiModelProperty(value = "品牌", name = "brandName")
    private String brandName;

    public String getDeliveryConfirmStatus() {
        return deliveryConfirmStatus;
    }

    public void setDeliveryConfirmStatus(String deliveryConfirmStatus) {
        this.deliveryConfirmStatus = deliveryConfirmStatus;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public Date getDeliveryDateStart() {
        return deliveryDateStart;
    }

    public void setDeliveryDateStart(Date deliveryDateStart) {
        this.deliveryDateStart = deliveryDateStart;
    }

    public Date getDeliveryDateEnd() {
        return deliveryDateEnd;
    }

    public void setDeliveryDateEnd(Date deliveryDateEnd) {
        this.deliveryDateEnd = deliveryDateEnd;
    }

    public Integer getPurchaseUserId() {
        return purchaseUserId;
    }

    public void setPurchaseUserId(Integer purchaseUserId) {
        this.purchaseUserId = purchaseUserId;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo;
    }

    public Integer getDifference() {
        return difference;
    }

    public void setDifference(Integer difference) {
        this.difference = difference;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public Date getCompleteShelvedStartDate() {
        return completeShelvedStartDate;
    }

    public void setCompleteShelvedStartDate(Date completeShelvedStartDate) {
        this.completeShelvedStartDate = completeShelvedStartDate;
    }

    public Date getCompleteShelvedEndDate() {
        return completeShelvedEndDate;
    }

    public void setCompleteShelvedEndDate(Date completeShelvedEndDate) {
        this.completeShelvedEndDate = completeShelvedEndDate;
    }

    public String getPurchaseUserName() {
        return purchaseUserName;
    }

    public void setPurchaseUserName(String purchaseUserName) {
        this.purchaseUserName = purchaseUserName;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getIsFbaQuick() {
        return isFbaQuick;
    }

    public void setIsFbaQuick(Integer isFbaQuick) {
        this.isFbaQuick = isFbaQuick;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getModelMerchandiserEmpId() {
        return modelMerchandiserEmpId;
    }

    public void setModelMerchandiserEmpId(Integer modelMerchandiserEmpId) {
        this.modelMerchandiserEmpId = modelMerchandiserEmpId;
    }

    public String getModelMerchandiserEmpName() {
        return modelMerchandiserEmpName;
    }

    public void setModelMerchandiserEmpName(String modelMerchandiserEmpName) {
        this.modelMerchandiserEmpName = modelMerchandiserEmpName;
    }

    public List<Integer> getAreaIdList() {
        return areaIdList;
    }

    public void setAreaIdList(List<Integer> areaIdList) {
        this.areaIdList = areaIdList;
    }
}
