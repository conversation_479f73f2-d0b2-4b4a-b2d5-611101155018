package com.nsy.api.wms.enumeration.stock;

import java.util.Arrays;
import java.util.Objects;

public enum PurchaseOrderTypeEnum {
    OUT_OF_STOCK_ORDER_APPLY("缺货订单申请", 1),
    CUSTOMIZATION_APPLY("定制申请", 2),
    FBA_SHIPMENT_APPLY("FBA发货申请", 3),
    PURCHASE_APPLY("采购申请", 4),
    NORMAL_PURCHASE_APPLY("正常采购", 5),
    RETURN_APPLY("退货返工", 6),
    DEVELOP_APPLY("开发申请", 7),
    BRANCH_COMPANY_APPLY("分公司申请", 8),
    SPOT_REPLENISHMENT_APPLY("现货补单", 9),
    MARKET_REPLENISHMENT_APPLY("市场补单", 10),
    GROUP_APPLY("小组申请", 11),
    FBA_REPLENISHMENT_DIRECT_DELIVERY_APPLY("FBA补货直发申请", 12),
    PLAN_PURCHASE_APPLY("计划申请", 13),
    PROCESSING_ORDER_APPLY("加工申请单", 14),
    OPERATIONAL_APPLY("运营申请", 15),
    SPOT_PURCHASE("现货采购", 16),
    QUANZHOU_DELIVERED("泉州仓发货", 18),
    FBA_QUICK("快进快出", 19),
    UNKNOWN("未知", 0);

    private final String desc;

    private final Integer intValue;

    PurchaseOrderTypeEnum(String desc, Integer intValue) {
        this.desc = desc;
        this.intValue = intValue;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getIntValue() {
        return intValue;
    }

    public static PurchaseOrderTypeEnum resolveByIntValue(Integer intValue) {
        return Arrays.stream(PurchaseOrderTypeEnum.values())
                .filter(e -> Objects.equals(e.getIntValue(), intValue))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
