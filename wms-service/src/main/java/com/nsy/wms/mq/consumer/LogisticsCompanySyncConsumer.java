package com.nsy.wms.mq.consumer;

import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.logistics.documents.LogisticsCompany;
import com.nsy.wms.business.domain.dto.stockout.LogisticsCompanyMessage;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.LogisticsCompanyService;
import com.nsy.wms.mq.TableListenerMessage;
import com.nsy.wms.mq.consumer.base.TableListenerConsumer;
import com.nsy.wms.repository.entity.stockout.LogisticsCompanyEntity;
import com.nsy.wms.utils.JsonMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 同步物流公司
 * <AUTHOR>
 * @date 2023/6/7 11:46
 */
@Component
public class LogisticsCompanySyncConsumer extends TableListenerConsumer<LogisticsCompany> {

    @Autowired
    private LogisticsCompanyService logisticsCompanyService;

    @KafkaListener(topics = KafkaConstant.NSY_LOGISTICS_COMPANY)
    public void newProductConsumer(ConsumerRecord<?, ?> record) {
        TableListenerMessage<LogisticsCompany> receiveMessage = JsonMapper.fromJson(record.value().toString(), LogisticsCompanyMessage.class);
        if (CollectionUtils.isEmpty(receiveMessage.getData()))
            return;
        processMessage(receiveMessage, receiveMessage.getData().get(0).getId().toString());
    }

    @Override
    protected void doProcessMessage(TableListenerMessage<LogisticsCompany> receiveMessage) {
        LoginInfoService.setName("LogisticsCompanySyncConsumer");
        receiveMessage.getData().forEach(message -> {
            LogisticsCompanyEntity entity = new LogisticsCompanyEntity();
            BeanUtils.copyProperties(message, entity);
            logisticsCompanyService.syncLogisticsCompany(receiveMessage.getType(), entity);
        });
        LoginInfoService.removeName();
    }

    @Override
    public String getTableName() {
        return KafkaConstant.NSY_LOGISTICS_COMPANY;
    }

    @Override
    public Class getBeanClass() {
        return LogisticsCompanyMessage.class;
    }
}
