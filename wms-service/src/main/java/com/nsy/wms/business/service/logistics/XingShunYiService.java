package com.nsy.wms.business.service.logistics;

import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.wms.business.manage.tms.request.OrderItemInfo;
import com.nsy.wms.business.service.logistics.annotation.LogisticsServiceHandler;
import com.nsy.wms.business.service.logistics.base.BaseLogisticsService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@LogisticsServiceHandler(logisticsCompany = LogisticsCompanyConstant.XING_SHUN_YI)
public class XingShunYiService extends BaseLogisticsService {

    @Override
    public void reSetUserPrice(List<OrderItemInfo> orderItemInfoList, String countryCode) {
        reSetUserPriceByTotalNum(orderItemInfoList);
    }
}
