package com.nsy.wms.business.service.stock;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.request.stock.StockLendStockListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockLendStockCountResponse;
import com.nsy.api.wms.response.stockin.StockLendStockListResponse;
import com.nsy.wms.business.domain.bo.stock.StockLendStockChangeLogAddBo;
import com.nsy.wms.business.domain.bo.stock.StockLendStockCreateBo;
import com.nsy.wms.business.domain.bo.stock.StockLendStockUpdateBo;
import com.nsy.wms.business.domain.bo.stock.StockLendStockUpdateStockAndWaitDistributedBo;
import com.nsy.wms.business.domain.bo.stock.StockLendStockUpdateWaitDistributedBo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.stock.StockLendStockEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockLendStockMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class StockLendStockService extends ServiceImpl<StockLendStockMapper, StockLendStockEntity> {
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockLendStockChangeLogService stockLendStockChangeLogService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;

    /**
     * 库存更新
     *
     * @param stockUpdate
     */
    @Transactional
    public void updateStock(StockLendStockUpdateBo stockUpdate) {
        StockLendStockEntity lendStock;
        if (stockUpdate.getQty() < 0) { //添加库存
            lendStock = getOrCreate(stockUpdate);
        } else {
            lendStock = getByBusinessTypeAndSku(stockUpdate.getBusinessType(), stockUpdate.getSku());
        }
        if (Objects.isNull(lendStock))
            throw new BusinessServiceException(String.format("【%s】借用库存不存在", stockUpdate.getSku()));

        //更新库存
        lendStock.setLendQty(lendStock.getLendQty() - stockUpdate.getQty());
        if (lendStock.getLendQty() < 0)
            throw new BusinessServiceException(String.format("【%s】借用库存不足", lendStock.getSku()));
        lendStock.setUpdateBy(loginInfoService.getName());
        lendStock.setUpdateDate(new Date());
        this.updateById(lendStock);

        //添加日志
        StockLendStockChangeLogAddBo changeLogAdd = new StockLendStockChangeLogAddBo();
        changeLogAdd.setLendStock(lendStock);
        changeLogAdd.setLend(stockUpdate.getLend());
        changeLogAdd.setLendReturn(stockUpdate.getLendReturn());
        changeLogAdd.setChangeLogType(stockUpdate.getChangeLogType());
        changeLogAdd.setQty(stockUpdate.getQty());
        changeLogAdd.setLogContent(stockUpdate.getLogContent());
        stockLendStockChangeLogService.addLog(changeLogAdd);

        delEmptyStock(lendStock);
    }

    /**
     * 更新库存 待分配
     *
     * @param bo
     */
    @Transactional
    public void updateStockWithWaitDistributed(StockLendStockUpdateStockAndWaitDistributedBo bo) {
        StockLendStockEntity lendStock;
        if (bo.getQty() < 0) { //添加库存
            lendStock = getOrCreate(bo);
        } else {
            lendStock = getByBusinessTypeAndSku(bo.getBusinessType(), bo.getSku());
        }
        if (Objects.isNull(lendStock))
            throw new BusinessServiceException(String.format("【%s】借用库存不存在", bo.getSku()));

        //更新库存
        lendStock.setLendQty(lendStock.getLendQty() - bo.getQty());
        if (lendStock.getLendQty() < 0)
            throw new BusinessServiceException(String.format("【%s】借用库存不足", lendStock.getSku()));
        lendStock.setWaitDistributedQty(lendStock.getWaitDistributedQty() + bo.getQty());
        lendStock.setWaitInferiorQty(lendStock.getWaitInferiorQty() + bo.getInferiorQty());
        lendStock.setUpdateBy(loginInfoService.getName());
        lendStock.setUpdateDate(new Date());
        this.updateById(lendStock);

        //添加日志
        StockLendStockChangeLogAddBo changeLogAdd = new StockLendStockChangeLogAddBo();
        changeLogAdd.setLendStock(lendStock);
        changeLogAdd.setLend(bo.getLend());
        changeLogAdd.setLendReturn(bo.getLendReturn());
        changeLogAdd.setChangeLogType(bo.getChangeLogType());
        changeLogAdd.setQty(bo.getQty());
        changeLogAdd.setLogContent(bo.getLogContent());
        stockLendStockChangeLogService.addLog(changeLogAdd);

        delEmptyStock(lendStock);
    }

    /**
     * 更新待分配数 待分配次品数
     *
     * @param bo
     */
    @Transactional
    public void updateWaitDistributed(StockLendStockUpdateWaitDistributedBo bo) {
        StockLendStockEntity lendStock = getByBusinessTypeAndSku(bo.getBusinessType(), bo.getSku());
        if (Objects.isNull(lendStock))
            throw new BusinessServiceException(String.format("【%s】借用库存不存在", bo.getSku()));

        //更新库存
        lendStock.setWaitDistributedQty(lendStock.getWaitDistributedQty() - bo.getQty());
        lendStock.setWaitInferiorQty(lendStock.getWaitInferiorQty() - bo.getInferiorQty());
        lendStock.setUpdateBy(loginInfoService.getName());
        lendStock.setUpdateDate(new Date());
        this.updateById(lendStock);

        delEmptyStock(lendStock);
    }

    /**
     * 删除空库存的数据
     *
     * @param stockLendStock
     */
    private void delEmptyStock(StockLendStockEntity stockLendStock) {
        if (stockLendStock.getLendQty() > 0)
            return;
        if (stockLendStock.getWaitDistributedQty() > 0)
            return;
        if (stockLendStock.getWaitInferiorQty() > 0)
            return;
        removeById(stockLendStock.getLendStockId());
    }

    /**
     * 获取，不存在则新增
     *
     * @param stockUpdate
     * @return
     */
    private StockLendStockEntity getOrCreate(StockLendStockUpdateBo stockUpdate) {
        StockLendStockEntity lendStock = getByBusinessTypeAndSku(stockUpdate.getBusinessType(), stockUpdate.getSku());
        //不存在库存记录则创建
        if (Objects.isNull(lendStock)) {
            if (Objects.isNull(stockUpdate.getLend()) && Objects.isNull(stockUpdate.getLendReturn()))
                throw new BusinessServiceException("借用单 和 归还单不能都为空");
            StockLendStockCreateBo createBo = new StockLendStockCreateBo();
            createBo.setSku(stockUpdate.getSku());
            createBo.setBusinessType(stockUpdate.getBusinessType());
            if (!Objects.isNull(stockUpdate.getLend())) {
                createBo.setSpaceId(stockUpdate.getLend().getSpaceId());
                createBo.setSpaceName(stockUpdate.getLend().getSpaceName());
            } else {
                createBo.setSpaceId(stockUpdate.getLendReturn().getSpaceId());
                createBo.setSpaceName(stockUpdate.getLendReturn().getSpaceName());
            }
            lendStock = create(createBo);
        }

        return lendStock;
    }

    /**
     * 创建库存记录
     *
     * @param bo
     * @return
     */
    private StockLendStockEntity create(StockLendStockCreateBo bo) {
        ProductSpecInfo specInfo = productSpecInfoService.getBySku(bo.getSku());

        StockLendStockEntity lendStock = new StockLendStockEntity();
        lendStock.setSpecId(specInfo.getSpecId());
        lendStock.setProductId(specInfo.getProductId());
        lendStock.setSku(specInfo.getSku());
        lendStock.setBarcode(specInfo.getBarcode());
        lendStock.setSpaceId(bo.getSpaceId());
        lendStock.setSpaceName(bo.getSpaceName());
        lendStock.setBusinessType(bo.getBusinessType());
        lendStock.setLendQty(0);
        lendStock.setWaitDistributedQty(0);
        lendStock.setWaitInferiorQty(0);
        lendStock.setCreateBy(loginInfoService.getName());
        lendStock.setCreateDate(new Date());
        lendStock.setUpdateBy(loginInfoService.getName());
        lendStock.setUpdateDate(new Date());

        this.save(lendStock);
        return lendStock;
    }

    /**
     * 通过部门查找有库存的列表
     *
     * @param businessType
     * @return
     */
    public List<StockLendStockEntity> getHasStockListByBusinessType(String businessType) {
        return this.list(new LambdaQueryWrapper<StockLendStockEntity>()
                .eq(StockLendStockEntity::getBusinessType, businessType)
                .gt(StockLendStockEntity::getLendQty, 0)
        );
    }

    /**
     * 通过部门和sku查找
     *
     * @param businessType
     * @param sku
     * @return
     */
    public StockLendStockEntity getByBusinessTypeAndSku(String businessType, String sku) {
        return this.getOne(new LambdaQueryWrapper<StockLendStockEntity>()
                .eq(StockLendStockEntity::getBusinessType, businessType)
                .eq(StockLendStockEntity::getSku, sku));
    }

    /**
     * 分页
     *
     * @param request
     * @return
     */
    public PageResponse<StockLendStockListResponse> pageList(StockLendStockListRequest request) {
        IPage page = new Page();
        page.setCurrent(request.getPageIndex());
        page.setSize(request.getPageSize());
        Page<StockLendStockListResponse> pageList = this.baseMapper.pageList(page, request);
        pageList.getRecords().forEach(temp -> temp.setBusinessTypeCN(temp.getBusinessType()));
        PageResponse<StockLendStockListResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(pageList.getRecords());
        pageResponse.setTotalCount(pageList.getTotal());
        return pageResponse;
    }

    /**
     * 统计
     *
     * @param request
     * @return
     */
    public StockLendStockCountResponse pageCount(StockLendStockListRequest request) {
        return this.baseMapper.pageCount(request);
    }
}
