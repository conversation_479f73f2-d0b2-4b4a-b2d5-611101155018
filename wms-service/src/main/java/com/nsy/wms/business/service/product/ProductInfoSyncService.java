package com.nsy.wms.business.service.product;

import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.pms.dto.product.ProductBaseInfoDTO;
import com.nsy.api.pms.dto.product.ProductDesignInfoDTO;
import com.nsy.api.pms.dto.product.ProductImageInfoDTO;
import com.nsy.api.pms.dto.product.ProductSpecBaseDTO;
import com.nsy.api.pms.dto.product.ProductSpecImageUrlDTO;
import com.nsy.api.wms.constants.CopyIgnoreFieldConstant;
import com.nsy.api.wms.constants.ProductInfoConstant;
import com.nsy.api.wms.domain.product.NsyProductProduct;
import com.nsy.api.wms.domain.product.ProductCategoryInfo;
import com.nsy.api.wms.domain.product.ProductSpec;
import com.nsy.api.wms.domain.product.ProductSpecChangeMessage;
import com.nsy.api.wms.enumeration.CustomsDeclareEnum;
import com.nsy.api.wms.enumeration.OperationEventEnum;
import com.nsy.api.wms.enumeration.bd.ProductWmsCategoryDeclarationTypeEnum;
import com.nsy.wms.business.manage.pms.PmsApiService;
import com.nsy.wms.business.manage.product.ProductApiService;
import com.nsy.wms.business.service.bd.SysCompanyService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.bd.SysCompanyEntity;
import com.nsy.wms.repository.entity.product.ProductCategoryMappingEntity;
import com.nsy.wms.repository.entity.product.ProductCustomsDeclareEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class ProductInfoSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductInfoSyncService.class);

    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    ProductLogService productLogService;
    @Autowired
    ProductApiService productApiService;
    @Autowired
    ProductCategoryMappingService productCategoryMappingService;
    @Autowired
    PmsApiService pmsApiService;
    @Autowired
    ProductCustomsDeclareService productCustomsDeclareService;
    @Resource
    ProductCustomsDeclareElementService productCustomsDeclareElementService;
    @Autowired
    ProductCategoryCustomsDeclareService categoryCustomsDeclareService;
    @Resource
    SysCompanyService sysCompanyService;
    @Autowired
    private LoginInfoService loginInfoService;


    @Transactional
    public void syncProductInfo(NsyProductProduct productChangeMessage) {
        //LOGGER.info("productChangeMessage: {}", JsonMapper.toJson(productChangeMessage));
        ProductBaseInfoDTO productInfoDTO = pmsApiService.getProductBaseInfoById(productChangeMessage.getId());
        if (Objects.isNull(productInfoDTO)) {
            return;
        }
        if (Objects.isNull(productInfoDTO.getErpProductId())) {
            return;
        }
        ProductInfoEntity entity = productInfoService.findTopByProductId(productInfoDTO.getProductId());
        if (!StringUtils.hasText(productInfoDTO.getProductSku())) {
            return;
        }
        boolean newProduct = false;
        if (entity == null) {
            entity = new ProductInfoEntity();
            entity.setCreateBy("ProductSyncDataConsumer");
            entity.setUpdateBy("ProductSyncDataConsumer");
            newProduct = true;
        }

        ProductDesignInfoDTO productDesignInfoById = pmsApiService.getProductDesignInfoById(productInfoDTO.getProductId());
        BeanUtils.copyProperties(productDesignInfoById, entity, CopyIgnoreFieldConstant.CREATE_DATE, CopyIgnoreFieldConstant.UPDATE_DATE);
        BeanUtils.copyProperties(productInfoDTO, entity, CopyIgnoreFieldConstant.CREATE_DATE, CopyIgnoreFieldConstant.UPDATE_DATE);
        LOGGER.info(TenantContext.getTenant());
        //根据商品设置Location
        SysCompanyEntity sysCompany = sysCompanyService.findByCompanyCodeIgnoreTenant(entity.getCompanyCode());
        TenantContext.setTenant(sysCompany.getLocation());

        entity.setSpu(productInfoDTO.getProductSku());
        entity.setDevelopStatus(productInfoDTO.getStatus());
        if (productInfoDTO.getCategoryId() != null) {
            ProductCategoryMappingEntity mappingEntity = productCategoryMappingService.findTopByCategoryId(productInfoDTO.getCategoryId(), ProductWmsCategoryDeclarationTypeEnum.GRANULAR_DECLARATION.name());
            entity.setWmsCategoryId(Objects.isNull(mappingEntity) ? null : mappingEntity.getWmsCategoryId());
            entity.setWmsCategoryName(Objects.isNull(mappingEntity) ? null : mappingEntity.getWmsCategoryName());
        }

        entity.setPackageUnit(ProductInfoConstant.PRODUCT_INFO_PACKAGE_UNIT_DEFAULT);
        productInfoService.saveOrUpdate(entity);
        if ("complete".equals(entity.getDevelopStatus()))
            syncProductCustomsDeclare(productInfoDTO);

        if (newProduct) {
            syncProductImage(entity.getProductId());
        }

        productLogService.saveProductLog(productInfoDTO.getProductId(), OperationEventEnum.PRODUCT_SYNC_DATA, entity);
        TenantContext.clear();
    }

    private void syncProductCustomsDeclare(ProductBaseInfoDTO productInfoDTO) {
        ProductCustomsDeclareEntity productCustomsDeclareEntity = productCustomsDeclareService.findBySpu(productInfoDTO.getProductSku());
        if (Objects.isNull(productCustomsDeclareEntity)) {
            productCustomsDeclareEntity = createProductCustomsDeclare(productInfoDTO);
            productCustomsDeclareElementService.updateElement(productCustomsDeclareEntity);
        }
    }

    private ProductCustomsDeclareEntity createProductCustomsDeclare(ProductBaseInfoDTO productInfoDTO) {
        ProductCustomsDeclareEntity productCustomsDeclareEntity = new ProductCustomsDeclareEntity();
        productCustomsDeclareEntity.setSpu(productInfoDTO.getProductSku());
        productCustomsDeclareEntity.setProductId(productInfoDTO.getProductId());
        ProductCategoryInfo productCategoryInfo = categoryCustomsDeclareService.getCategoryCustomsDeclare(productInfoDTO.getProductSku());
        productCustomsDeclareEntity.setCustomsDeclareCn(productCategoryInfo.getCustomsDeclareCn());
        productCustomsDeclareEntity.setCustomsDeclareEn(productCategoryInfo.getCustomsDeclareEn());
        productCustomsDeclareEntity.setCreateBy(loginInfoService.getName());
        productCustomsDeclareEntity.setUpdateBy(loginInfoService.getName());
        productCustomsDeclareEntity.setDeclareType(CustomsDeclareEnum.DECLARE_NORMAL.getValue());

        //设置区域
        SysCompanyEntity sysCompany = sysCompanyService.findByCompanyCodeIgnoreTenant(productInfoDTO.getCompanyCode());
        productCustomsDeclareEntity.setLocation(sysCompany.getLocation());
        productCustomsDeclareService.save(productCustomsDeclareEntity);
        return productCustomsDeclareEntity;
    }

    @Transactional
    public void syncProductSpecInfoByIds(List<Integer> specIds) {
        specIds.forEach(specId -> {
            ProductSpec productSpecDetail = productApiService.getProductSpecDetail(specId);
            ProductSpecChangeMessage productSpecChangeMessage = new ProductSpecChangeMessage();
            BeanUtils.copyProperties(productSpecDetail, productSpecChangeMessage);
            this.syncProductSpecInfo(productSpecChangeMessage);
        });
    }

    @Transactional
    public void syncProductSpecInfo(ProductSpecChangeMessage productSpecChangeMessage) {
        ProductSpecBaseDTO productSpecDTO = pmsApiService.getProductSpecBaseBySpecId(productSpecChangeMessage.getId());
        ProductSpecInfoEntity entity = productSpecInfoService.findTopBySku(productSpecDTO.getSpecSku());
        if (entity == null)
            entity = productSpecInfoService.findTopBySpecId(productSpecDTO.getSpecId());
        boolean newSpec = false;
        if (entity == null) {
            entity = new ProductSpecInfoEntity();
            entity.setCreateBy(loginInfoService.getName());
            entity.setUpdateBy(loginInfoService.getName());
            newSpec = true;
        }
        BeanUtils.copyProperties(productSpecDTO, entity, CopyIgnoreFieldConstant.CREATE_DATE, CopyIgnoreFieldConstant.UPDATE_DATE);
        entity.setActualWeight(productSpecDTO.getWeight());
        entity.setWeight(productSpecDTO.getEstimateWeight());
        entity.setSkc(productSpecDTO.getColorSku());
        entity.setSku(productSpecDTO.getSpecSku());
        entity.setSpecId(productSpecDTO.getSpecId());
        entity.setSort(productSpecDTO.getSort());

        productSpecInfoService.saveOrUpdate(entity);
        productLogService.saveProductSpecLog(productSpecDTO.getProductId(), OperationEventEnum.PRODUCT_SYNC_DATA, entity);

        if (newSpec) {
            this.syncProductSpecImageInfo(entity);
        }

    }


    @Transactional
    public void syncProductSpecImageInfo(ProductSpecInfoEntity entity) {
        ProductSpecImageUrlDTO productSpecDTO = pmsApiService.getProductSpecImageBySpecId(entity.getSpecId());
        if (Objects.isNull(productSpecDTO))
            return;

        if (StringUtils.hasText(productSpecDTO.getImageUrl())) {
            entity.setImageUrl(productSpecDTO.getImageUrl());
            entity.setPreviewImageUrl(productSpecDTO.getImageUrl() + ProductInfoConstant.PRODUCT_INFO_PREVIEW_IMAGE_URL);
            entity.setThumbnailImageUrl(productSpecDTO.getImageUrl() + ProductInfoConstant.PRODUCT_INFO_THUMBNAIL_IMAGE_URL);
        }
        productSpecInfoService.updateById(entity);
    }


    @Transactional
    public void syncProductImage(Integer productId) {
        ProductInfoEntity entity = productInfoService.findTopByProductId(productId);
        if (entity == null)
            return;
        //更新商品主图
        ProductImageInfoDTO productImageInfoDTO = pmsApiService.getProductImageInfoById(productId);
        if (Objects.isNull(productImageInfoDTO))
            return;

        if (StringUtils.hasText(productImageInfoDTO.getImageUrl())) {
            entity.setImageUrl(productImageInfoDTO.getImageUrl());
            entity.setPreviewImageUrl(productImageInfoDTO.getImageUrl() + ProductInfoConstant.PRODUCT_INFO_PREVIEW_IMAGE_URL);
            entity.setThumbnailImageUrl(productImageInfoDTO.getImageUrl() + ProductInfoConstant.PRODUCT_INFO_THUMBNAIL_IMAGE_URL);
        }

        productInfoService.updateById(entity);


    }

    public void syncProductSpecInfoImageByIds(List<Integer> idList) {
        List<ProductSpecInfoEntity> productSpecInfoEntities = productSpecInfoService.listByIds(idList);
        productSpecInfoEntities.forEach(item -> {
            this.syncProductSpecImageInfo(item);
        });
    }
}
