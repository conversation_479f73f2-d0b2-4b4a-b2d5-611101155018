package com.nsy.wms.business.manage.activiti;

import com.nsy.api.activiti.dto.request.ActiveProcessInstancesRequest;
import com.nsy.api.activiti.dto.request.AddTaskCandidateUserRequest;
import com.nsy.api.activiti.dto.request.AssignTaskRequest;
import com.nsy.api.activiti.dto.request.BatchCompleteRequest;
import com.nsy.api.activiti.dto.request.BatchJumpRequest;
import com.nsy.api.activiti.dto.request.ClaimTaskRequest;
import com.nsy.api.activiti.dto.request.CompleteTaskRequest;
import com.nsy.api.activiti.dto.request.DeleteTaskRequest;
import com.nsy.api.activiti.dto.request.JumpTaskRequest;
import com.nsy.api.activiti.dto.request.MyTaskQueryRequest;
import com.nsy.api.activiti.dto.request.SetVariableRequest;
import com.nsy.api.activiti.dto.request.StartProcessRequest;
import com.nsy.api.activiti.dto.request.SuspendProcessInstancesRequest;
import com.nsy.api.activiti.dto.request.TaskCandidateUser;
import com.nsy.api.activiti.dto.request.UpdateTaskAssigneeRequest;
import com.nsy.api.activiti.dto.request.UpdateTaskRequest;
import com.nsy.api.activiti.dto.response.TaskQueryResponse;
import com.nsy.api.activiti.dto.response.TaskResponse;
import com.nsy.api.activiti.feign.WorkflowFeignClient;
import com.nsy.api.core.apicore.base.UserInfo;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.privilege.LoginInfoService;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.wms.enumeration.ActivitiBusinessKeyPrefixEnum;
import com.nsy.api.wms.enumeration.FlowTaskDefinitionEnum;
import com.nsy.wms.business.domain.dto.active.FlowTaskDto;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.utils.JsonMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/11 9:52
 */
@Service
public class FlowOperateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(FlowOperateService.class);
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private WorkflowFeignClient workflowFeignClient;
    @Value("${nsy.service.url.activiti}")
    private String activitiServiceUrl;
    @Inject
    private RestTemplate restTemplate;

    public String genBusinessKey(Integer taskId, ActivitiBusinessKeyPrefixEnum prefixEnum) {
        return String.format("%s:%s", prefixEnum.name(), taskId);
    }

    public void completeTaskByTaskId(String taskId, Map<String, Object> variable) {
        UserInfo assignee = loginInfoService.getCurrentUserInfo();
        this.completeTaskByTaskId(taskId, variable, assignee);
    }

    public void completeTaskByTaskId(String taskId, Map<String, Object> variable, UserInfo assignee) {
        CompleteTaskRequest completeTaskRequest = buildCompleteTaskByIdReq(taskId, variable, assignee);
        completeTask(completeTaskRequest);
    }


    public void completeTaskByTaskId(String taskId, Map<String, Object> variable, UserInfo assignee, String taskName) {
        CompleteTaskRequest completeTaskRequest = buildCompleteTaskByIdReq(taskId, variable, assignee, taskName);
        completeTask(completeTaskRequest);
    }


    public void completeTask(CompleteTaskRequest completeTaskRequest) {
        workflowFeignClient.completeTask(completeTaskRequest);
    }

    public void batchCompleteByBusinessKeyList(List<CompleteTaskRequest> completeTaskRequestList) {
        BatchCompleteRequest batchCompleteRequest = new BatchCompleteRequest();
        batchCompleteRequest.setCompleteTaskRequest(completeTaskRequestList);
        workflowFeignClient.batchCompleteInstanceByBusinessKey(batchCompleteRequest);
    }


    public void completeTaskByBusinessKey(String businessKey, Map<String, Object> variable, UserInfo assignee) {
        TaskResponse taskResponse = queryTaskByBusinessKey(businessKey);
        if (Objects.isNull(taskResponse)) {
            throw new BusinessServiceException("工作流任务不存在");
        }
        this.completeTaskByTaskId(taskResponse.getTaskId(), variable, assignee);
    }


    public void completeTaskByBusinessKey(String businessKey, Map<String, Object> variable, UserInfo assignee, String taskName) {
        TaskResponse taskResponse = queryTaskByBusinessKey(businessKey);
        if (Objects.isNull(taskResponse)) {
            throw new BusinessServiceException("工作流任务不存在");
        }
        this.completeTaskByTaskId(taskResponse.getTaskId(), variable, assignee, taskName);
    }


    public FlowTaskDto getTerminateActiveTask(String businessKey, FlowTaskDefinitionEnum flowTaskDefinition) {
        if (StringUtils.isBlank(businessKey)) {
            throw new IllegalArgumentException("businessKey required");
        }
        if (Objects.isNull(flowTaskDefinition)) {
            throw new IllegalArgumentException("flowTaskDefinition required");
        }

        MyTaskQueryRequest taskQueryRequest = new MyTaskQueryRequest();
        taskQueryRequest.setBusinessKey(businessKey);
        taskQueryRequest.setTaskKey(flowTaskDefinition.getTaskDefinitionKey());
        taskQueryRequest.setProcessDefinitionKey(flowTaskDefinition.getProcessDefinitionKey());
        TaskQueryResponse taskQueryResponse = workflowFeignClient.queryTasks(taskQueryRequest);
        if (Objects.isNull(taskQueryResponse)) {
            return null;
        }
        List<TaskResponse> tasks = taskQueryResponse.getTasks();
        if (tasks.isEmpty()) {
            return null;
        }

        if (tasks.size() > 1) {
            LOGGER.warn("there seems to be not only one result, businessKey = {}, flowTaskDefinition = {}",
                    businessKey, flowTaskDefinition);
        }

        FlowTaskDto flowTaskDto = new FlowTaskDto();
        BeanUtils.copyProperties(tasks.get(0), flowTaskDto);
        return flowTaskDto;
    }


    public FlowTaskDto getTerminateActiveTask(String businessKey, FlowTaskDefinitionEnum flowTaskDefinition, UserInfo assignee) {
        if (StringUtils.isBlank(businessKey)) {
            throw new IllegalArgumentException("businessKey required");
        }
        if (Objects.isNull(flowTaskDefinition)) {
            throw new IllegalArgumentException("flowTaskDefinition required");
        }
        if (Objects.isNull(assignee)) {
            throw new IllegalArgumentException("assignee required");
        }

        MyTaskQueryRequest taskQueryRequest = new MyTaskQueryRequest();
        taskQueryRequest.setBusinessKey(businessKey);
        taskQueryRequest.setTaskKey(flowTaskDefinition.getTaskDefinitionKey());
        taskQueryRequest.setProcessDefinitionKey(flowTaskDefinition.getProcessDefinitionKey());
        taskQueryRequest.setAssignee(assignee.getUserAccount());

        TaskQueryResponse taskQueryResponse = workflowFeignClient.queryTasks(taskQueryRequest);
        if (Objects.isNull(taskQueryResponse)) {
            return null;
        }
        List<TaskResponse> tasks = taskQueryResponse.getTasks();
        if (tasks.isEmpty()) {
            return null;
        }

        if (tasks.size() > 1) {
            LOGGER.warn("there seems to be not only one result, businessKey = {}, flowTaskDefinition = {}, assignee = {}",
                    businessKey, flowTaskDefinition, assignee.getUserAccount());
        }

        FlowTaskDto flowTaskDto = new FlowTaskDto();
        BeanUtils.copyProperties(tasks.get(0), flowTaskDto);
        return flowTaskDto;
    }


    public void batchClaimTask(List<String> businessKeys, FlowTaskDefinitionEnum flowTaskDefinition, UserInfo claimer) {
        if (CollectionUtils.isEmpty(businessKeys)) {
            LOGGER.info("batchClaimTask ignore, empty businessKeys");
            return;
        }
        if (flowTaskDefinition == null || FlowTaskDefinitionEnum.UNKNOWN == flowTaskDefinition) {
            throw new IllegalArgumentException("flowTaskDefinition illegal");
        }
        if (Objects.isNull(claimer)) {
            throw new IllegalArgumentException("claimer required");
        }

        ClaimTaskRequest claimTaskRequest = new ClaimTaskRequest();
        claimTaskRequest.setClaimUser(claimer.getUserAccount());
        claimTaskRequest.setBusinessKey(businessKeys);
        claimTaskRequest.setTaskName(flowTaskDefinition.getTaskName());
        workflowFeignClient.claimTask(claimTaskRequest);
    }


    public void batchClaimTask(List<String> businessKeys, FlowTaskDefinitionEnum flowTaskDefinition, String userAccount) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserAccount(userAccount);
        batchClaimTask(businessKeys, flowTaskDefinition, userInfo);
    }


    public void batchCancelClaimTaskTask(List<String> businessKeys, FlowTaskDefinitionEnum flowTaskDefinition, UserInfo assignee) {
        if (CollectionUtils.isEmpty(businessKeys)) {
            LOGGER.info("batchCancelClaimTaskTask ignore, empty businessKeys");
            return;
        }
        if (flowTaskDefinition == null || FlowTaskDefinitionEnum.UNKNOWN == flowTaskDefinition) {
            throw new IllegalArgumentException("flowTaskDefinition illegal");
        }
        if (Objects.isNull(assignee)) {
            throw new IllegalArgumentException("assignee required");
        }

        ClaimTaskRequest claimTaskRequest = new ClaimTaskRequest();
        claimTaskRequest.setClaimUser(assignee.getUserAccount());
        claimTaskRequest.setBusinessKey(businessKeys);
        claimTaskRequest.setTaskName(flowTaskDefinition.getTaskName());
        workflowFeignClient.unClaimTask(claimTaskRequest);
    }


    public void batchSuspendProcessInstance(List<String> businessKeys) {
        if (CollectionUtils.isEmpty(businessKeys)) {
            LOGGER.info("batchSuspendProcessInstance ignore, empty businessKeys");
            return;
        }
        SuspendProcessInstancesRequest suspendProcessInstancesRequest = new SuspendProcessInstancesRequest();
        suspendProcessInstancesRequest.setBusinessKeys(new HashSet<>(businessKeys));
        workflowFeignClient.suspendProcessInstancesByBusinessKey(suspendProcessInstancesRequest);
    }


    public void batchActiveProcessInstance(List<String> businessKeys) {
        if (CollectionUtils.isEmpty(businessKeys)) {
            LOGGER.info("batchActiveProcessInstance ignore, empty businessKeys");
            return;
        }
        ActiveProcessInstancesRequest request = new ActiveProcessInstancesRequest();
        request.setBusinessKeys(new HashSet<>(businessKeys));
        workflowFeignClient.activeProcessInstancesByBusinessKey(request);
    }


    public List<TaskResponse> queryTasks(MyTaskQueryRequest flowRequest) {
        try {
            TaskQueryResponse taskQueryResponse = workflowFeignClient.queryTasks(flowRequest);
            if (taskQueryResponse == null) {
                LOGGER.warn("queryTasks returns null, flowRequest = {}", JsonMapper.toJson(flowRequest));
                return Collections.emptyList();
            }
            return taskQueryResponse.getTasks();
        } catch (RuntimeException e) {
            LOGGER.error("queryTasks error, flowRequest = {}, cause = {}", JsonMapper.toJson(flowRequest), e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    public List<TaskResponse> queryTasksByBusinessKeys(MyTaskQueryRequest taskQueryRequest) {
        try {
            TaskQueryResponse taskQueryResponse = workflowFeignClient.queryTasksByBusinessKeys(taskQueryRequest);
            if (taskQueryResponse == null) {
                LOGGER.warn("queryTasksByBusinessKeys returns null, flowRequest = {}", JsonMapper.toJson(taskQueryRequest));
                return Collections.emptyList();
            }
            return taskQueryResponse.getTasks();
        } catch (RuntimeException e) {
            LOGGER.error("queryTasksByBusinessKeys error, flowRequest = {}, cause = {}", JsonMapper.toJson(taskQueryRequest), e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    public List<TaskResponse> queryTasksByBusinessKeys(List<String> businessKeys) {
        if (CollectionUtils.isEmpty(businessKeys)) {
            return Collections.emptyList();
        }
        MyTaskQueryRequest request = new MyTaskQueryRequest();
        request.setBusinessKeys(businessKeys);
        return queryTasksByBusinessKeys(request);
    }


    public TaskResponse queryTaskByBusinessKey(String businessKey) {
        try {
            MyTaskQueryRequest request = new MyTaskQueryRequest();
            request.setBusinessKeys(Collections.singletonList(businessKey));
            TaskQueryResponse taskQueryResponse = workflowFeignClient.queryTasksByBusinessKeys(request);
            if (taskQueryResponse == null) {
                LOGGER.warn("queryTasksByBusinessKeys returns null, flowRequest = {}", businessKey);
                return null;
            }
            if (CollectionUtils.isEmpty(taskQueryResponse.getTasks())) {
                return null;
            }
            return taskQueryResponse.getTasks().get(0);
        } catch (RuntimeException e) {
            LOGGER.error("queryTasksByBusinessKeys error, flowRequest = {}, cause = {}", businessKey, e.getMessage(), e);
        }
        return null;
    }


    public List<TaskResponse> queryTasksByBusinessKey(String businessKey) {
        try {
            TaskQueryResponse taskQueryResponse = workflowFeignClient.queryTasksByBusinessKey(businessKey);
            if (taskQueryResponse == null) {
                LOGGER.warn("queryTasksByBusinessKey returns null, businessKey = {}", businessKey);
                return Collections.emptyList();
            }
            return taskQueryResponse.getTasks();
        } catch (RuntimeException e) {
            LOGGER.error("queryTasksByBusinessKey error, businessKey = {}, cause = {}", businessKey, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    private CompleteTaskRequest buildCompleteTaskByIdReq(String taskId, Map<String, Object> variable, UserInfo assignee) {
        CompleteTaskRequest completeTaskRequest = new CompleteTaskRequest();
        completeTaskRequest.setTaskId(taskId);
        completeTaskRequest.setAssignee(assignee.getUserAccount());
        completeTaskRequest.setVariables(variable);
        completeTaskRequest.setLocalVariable(true);
        completeTaskRequest.setOperateBy(assignee.getUserAccount());
        return completeTaskRequest;
    }

    private CompleteTaskRequest buildCompleteTaskByIdReq(String taskId, Map<String, Object> variable, UserInfo assignee, String taskName) {
        CompleteTaskRequest completeTaskRequest = new CompleteTaskRequest();
        completeTaskRequest.setTaskId(taskId);
        completeTaskRequest.setAssignee(assignee.getUserAccount());
        completeTaskRequest.setVariables(variable);
        completeTaskRequest.setTaskName(taskName);
        completeTaskRequest.setLocalVariable(true);
        completeTaskRequest.setOperateBy(assignee.getUserAccount());
        return completeTaskRequest;
    }

    public void startProcessInstanceByKey(StartProcessRequest startProcessRequest) {
        try {
            workflowFeignClient.startProcessInstanceByKey(startProcessRequest);
        } catch (RuntimeException e) {
            LOGGER.error("startProcessInstanceByKey error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("流程开始失败，请联系管理员！" + e.getMessage(), e);
        }
    }


    public void setVariables(SetVariableRequest request) {
        try {
            workflowFeignClient.setVariables(request);
        } catch (RuntimeException e) {
            LOGGER.error("setVariables error, cause ={}", e.getMessage(), e);
        }
    }


    public void updateTaskAssign(UpdateTaskAssigneeRequest request) {
        try {
            workflowFeignClient.updateTaskAssign(request);
        } catch (RuntimeException e) {
            LOGGER.error("updateTaskAssign error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("更换任务负责人错误，请联系管理员！" + e.getMessage(), e);
        }
    }


    public void updateTaskAssignOneByOne(UpdateTaskRequest request) {
        try {
            workflowFeignClient.updateTaskAssignOneByOne(request);
        } catch (RuntimeException e) {
            LOGGER.error("updateTaskAssign error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("更换任务负责人错误，请联系管理员！" + e.getMessage(), e);
        }
    }


    public void updateTasks(UpdateTaskRequest request) {
        try {
            workflowFeignClient.updateTasks(request);
        } catch (RuntimeException e) {
            LOGGER.error("updateTasks error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("任务指派错误，请联系管理员！" + e.getMessage(), e);
        }
    }

    public void addCandidateUser(List<TaskCandidateUser> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        try {
            AddTaskCandidateUserRequest request = new AddTaskCandidateUserRequest();
            request.setTaskCandidateUserList(list);
            workflowFeignClient.addCandidateUser(request);
        } catch (RuntimeException e) {
            LOGGER.error("addCandidateUser error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("任务添加候选人失败，请联系管理员！" + e.getMessage(), e);
        }
    }

    public void deleteTask(DeleteTaskRequest request) {
        try {
            workflowFeignClient.deleteTask(request);
        } catch (RuntimeException e) {
            LOGGER.error("updateTaskAssign error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("删除工作流失败，请联系管理员！" + e.getMessage(), e);
        }
    }


    public void deleteCandidateUser(List<TaskCandidateUser> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        try {
            AddTaskCandidateUserRequest request = new AddTaskCandidateUserRequest();
            request.setTaskCandidateUserList(list);
            workflowFeignClient.deleteCandidateUser(request);
        } catch (RuntimeException e) {
            LOGGER.error("deleteCandidateUser error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("任务移除候选人失败，请联系管理员！" + e.getMessage(), e);
        }
    }


    public void jumpToTargetNode(List<JumpTaskRequest> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        try {
            BatchJumpRequest request = new BatchJumpRequest();
            request.setJumpTaskRequestList(list);
            workflowFeignClient.jumpToTargetNode(request);
        } catch (RuntimeException e) {
            LOGGER.error("jumpToTargetNode error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("任务跳转失败，请联系管理员！" + e.getMessage(), e);
        }
    }


    public void assignTask(AssignTaskRequest request) {
        if (Objects.isNull(request)) {
            return;
        }
        try {
            workflowFeignClient.assignTask(request);
        } catch (RuntimeException e) {
            LOGGER.error("assignTask error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("任务指派失败，请联系管理员！" + e.getMessage(), e);
        }
    }

    public void claimTask(ClaimTaskRequest request) {
        if (Objects.isNull(request)) {
            return;
        }
        try {
            workflowFeignClient.claimTask(request);
        } catch (RuntimeException e) {
            LOGGER.error("assignTask error, cause ={}", e.getMessage(), e);
            throw new BusinessServiceException("任务领取失败，请联系管理员！" + e.getMessage(), e);
        }
    }

    /**
     * 流程实例配置-获取任务候选人完整信息
     *
     * @param taskDefKey 节点ID
     * @param companyId  公司id
     * @return
     */
    public List<SysUserInfo> getConfigCandidateUser(String taskDefKey, Integer companyId) {
        String uri = String.format("%s/config/candidate-user?taskDefKey=%s&companyId=%s", activitiServiceUrl, taskDefKey, companyId);
        try {
            ResponseEntity<String> responseEntity = this.restTemplate.getForEntity(uri, String.class);
            LOGGER.debug(JsonMapper.toJson(responseEntity));
            if (Objects.nonNull(responseEntity.getBody())) {
                return JSONUtils.fromJSONArray(responseEntity.getBody(), SysUserInfo.class);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LOGGER.info("获取[{}]流程候选人失败，无法获取流程实例候选人", taskDefKey);
        return Collections.emptyList();
    }
}

