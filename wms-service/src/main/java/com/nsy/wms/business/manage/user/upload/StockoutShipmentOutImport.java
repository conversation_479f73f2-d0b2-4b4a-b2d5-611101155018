package com.nsy.wms.business.manage.user.upload;

/**
 * 越库装箱导入
 *
 * <AUTHOR>
 * 2021-08-24
 */
public class StockoutShipmentOutImport {

    private String stockoutOrderNo;

    private String shipmentBoxCode;

    private String sku;

    private String qty;

    private String boxSize;

    private String weight;

    /**
     * 错误信息
     */
    private String errorMsg;

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getShipmentBoxCode() {
        return shipmentBoxCode;
    }

    public void setShipmentBoxCode(String shipmentBoxCode) {
        this.shipmentBoxCode = shipmentBoxCode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getQty() {
        return qty;
    }

    public void setQty(String qty) {
        this.qty = qty;
    }

    public String getBoxSize() {
        return boxSize;
    }

    public void setBoxSize(String boxSize) {
        this.boxSize = boxSize;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }
}
