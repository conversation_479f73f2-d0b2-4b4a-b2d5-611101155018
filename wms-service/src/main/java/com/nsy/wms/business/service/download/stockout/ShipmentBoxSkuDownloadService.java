package com.nsy.wms.business.service.download.stockout;

import com.alibaba.fastjson.JSONObject;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.ShipmentDownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockout.StockoutShipmentDownloadService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ShipmentBoxSkuDownloadService implements IDownloadService {

    @Autowired
    private StockoutShipmentDownloadService downloadService;
    @Autowired
    private ShipmentAllShippedDownloadService shipmentAllShippedDownloadService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_SHIPMENT_BOX_SKU;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        ShipmentDownloadRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), ShipmentDownloadRequest.class);
        DownloadResponse response = new DownloadResponse();
        if (CollectionUtils.isEmpty(downloadRequest.getShipmentIds())) {
            shipmentAllShippedDownloadService.buildShipmentId(downloadRequest, request, response);
        }
        if (CollectionUtils.isEmpty(downloadRequest.getShipmentIds())) {
            response.setTotalCount(0L);
            return response;
        }

        List<ShipmentBoxSkuExport> resultList = downloadService.shipmentBoxSkuList(downloadRequest);
        Map<String, Boolean> boxIndexExistMap = new HashMap<>();
        List<ShipmentBoxSkuExport> collect = resultList.stream().peek(item -> {
            List<String> list = new ArrayList<>();
            list.add(item.getColor() == null ? "" : item.getColor());
            list.add(item.getSize() == null ? "" : item.getSize());
            item.setColorAndSize(JSONObject.toJSONString(list));
            if (item.getQty() != null && StringUtils.hasText(item.getInvoicePrice())) {
                item.setTotalPrice(new BigDecimal(item.getInvoicePrice()).multiply(new BigDecimal(item.getQty())).toPlainString());
            }
            if (boxIndexExistMap.containsKey(item.getBoxIndex())) {
                item.setBoxIndex(null);
                item.setBoxSize(null);
                item.setVolumeWeight(null);
                item.setWeight(null);
            } else {
                boxIndexExistMap.put(item.getBoxIndex(), Boolean.TRUE);
            }
        }).collect(Collectors.toList());
        response.setDataJsonStr(JsonMapper.toJson(collect));
        return response;
    }
}
