package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemSourcePositionBo;
import com.nsy.api.wms.domain.stockout.StockoutBatchScanTypeInfo;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitTaskItemSowWall;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskStatus;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitScanBarcodeRequest;
import com.nsy.api.wms.request.stockout.StockoutReplaceSkuRequest;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskCompleteScanResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskRefreshResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskSkuResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutBatchSplitTaskMinusPickingStockBo;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockTransferCrossSpaceService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchSplitTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchSplitTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Service
public class StockoutBatchSplitSecondaryCommonService extends ServiceImpl<StockoutBatchSplitTaskMapper, StockoutBatchSplitTaskEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutBatchSplitSecondaryCommonService.class);
    @Autowired
    StockoutBatchSplitTaskItemMapper taskItemMapper;
    @Autowired
    StockoutBatchSplitTaskService splitTaskService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutBatchSplitTaskItemService splitItemService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockoutBatchSplitSecondaryService stockoutBatchSplitSecondaryService;
    @Autowired
    StockoutBatchOrderItemService stockoutBatchOrderItemService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockTransferCrossSpaceService stockTransferCrossSpaceService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutLogQueueService stockoutLogQueueService;
    @Autowired
    StockoutBatchSplitCommonService sortCommonService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    StockoutReplaceSkuService stockoutReplaceSkuService;
    @Autowired
    StockoutEasyScanTaskService stockoutEasyScanTaskService;
    @Autowired
    StockoutOrderNoticeLackService noticeLackService;
    @Autowired
    StockoutBatchSplitCommonCheckService sortCommonCheckService;
    @Autowired
    StockoutBatchSplitCommonBuildService sortCommonBuildService;
    @Autowired
    StockoutBatchSplitService stockoutBatchSplitCommonService;
    @Autowired
    private StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Autowired
    private StockoutBatchScanTypeService scanTypeService;

    private static final Integer COLOR_YELLOW = 2; // 打钩/分拣口颜色/黄色/红色

    /**
     * 获取分拣口
     * 1校验是否需要撤货，需要撤货走撤货处理流程；不需要撤货返回对应需要分拣的item
     * 2.分配分拣口：
     * 判断item对应的出库单是否有分拣口，若有分拣口直接返回；若无分拣口，分配分拣口(需判断是否已经所有分拣口已满)
     */
    @Transactional
    public StockoutBatchSplitTaskSkuResponse barcodeScanGetOutlet(StockoutBatchSplitScanBarcodeRequest request) {
        sortCommonCheckService.validateSplitScanRequest(request);
        StockoutBatchSplitTaskSkuResponse response = new StockoutBatchSplitTaskSkuResponse();
        List<StockoutBatchSplitTaskItemSowWall> allItemList = taskItemMapper.searchTaskItemByBatchId(request.getBatchId());
        if (CollectionUtils.isEmpty(allItemList)) {
            throw new BusinessServiceException(String.format("找不到波次号：【%s】的分拣任务信息", request.getBatchId()));
        }
        // 校验撤货，需要撤货走撤货处理，需要撤货直接返回；不需要撤货返回对应需要分拣的item
        StockoutBatchSplitTaskItemSowWall splitTaskItemSowWall = sortCommonCheckService.checkWithdrawal(allItemList, request, response);
        if (splitTaskItemSowWall == null) {
            return response;
        }
        String stockoutOrderNo = splitTaskItemSowWall.getStockoutOrderNo();
        int taskItemId = splitTaskItemSowWall.getTaskItemId();
        if (request.getItemId() != null) {
            taskItemId = request.getItemId();
            if (StringUtils.hasText(request.getStockoutOrderNo()))
                stockoutOrderNo = request.getStockoutOrderNo();
        }
        // 分配分拣口
        int stockoutOrderOutlet = getStockoutOrderOutlet(stockoutOrderNo, allItemList);
        if (stockoutOrderOutlet != 0) {
            LOGGER.info("barcode:{} 对应出库单：{} 存在分拣口：{} -----------", request.getBarcode(), stockoutOrderNo, stockoutOrderOutlet);
            sortCommonBuildService.buildScanResponse(response, taskItemId, splitTaskItemSowWall.getScanQty(), stockoutOrderOutlet);
            if (splitTaskItemSowWall.getOutlet() == null || splitTaskItemSowWall.getOutlet() == 0)
                updateItemOutlet(splitTaskItemSowWall.getTaskId(), stockoutOrderNo, stockoutOrderOutlet);
        } else {
            LOGGER.info("barcode:{} 对应出库单：{} 不存在分拣口，分配分拣口-------", request.getBarcode(), stockoutOrderNo);
            splitItemNoOutlet(request, allItemList, splitTaskItemSowWall, response);
        }
        response.setThumbnailImageUrl(splitTaskItemSowWall.getThumbnailImageUrl());
        response.setStockoutOrderNo(stockoutOrderNo);
        String finalStockoutOrderNo = stockoutOrderNo;
        List<StockoutBatchSplitTaskItemSowWall> filterItemList = allItemList.stream().filter(item -> finalStockoutOrderNo.equals(item.getStockoutOrderNo())).collect(Collectors.toList());
        int stockoutOrderProductQty = filterItemList.stream().mapToInt(StockoutBatchSplitTaskItemSowWall::getBatchQty).sum();
        response.setStockoutOrderProductQty(stockoutOrderProductQty);
        //配置换码标注
        StockoutReplaceSkuRequest replaceSkuRequest = new StockoutReplaceSkuRequest();
        replaceSkuRequest.setBatchId(request.getBatchId());
        replaceSkuRequest.setBarcode(request.getBarcode());
        replaceSkuRequest.setStockoutOrderNo(stockoutOrderNo);
        response.setStockoutReplaceSkuResponse(stockoutReplaceSkuService.getReplaceSku(replaceSkuRequest));
        return response;
    }


    public void splitItemNoOutlet(StockoutBatchSplitScanBarcodeRequest request, List<StockoutBatchSplitTaskItemSowWall> allItemList, StockoutBatchSplitTaskItemSowWall splitTaskItemSowWall, StockoutBatchSplitTaskSkuResponse response) {
        List<Integer> allOutletList = new ArrayList<>();
        allItemList.stream().filter(item -> item.getOutlet() != null && item.getOutlet() != 0).forEach(m -> allOutletList.add(m.getOutlet()));
        int outlet = 1;
        if (!CollectionUtils.isEmpty(allOutletList)) {
            LOGGER.info("部分分拣口已占用，准备分配分拣口-------");
            List<Integer> filterOutletList = allOutletList.stream().distinct().collect(Collectors.toList());
            Integer totalOutlet = getTotalOutlet(request.getSecondSortingType());
            checkOutlet(filterOutletList.size(), totalOutlet);
            outlet = buildOutlet(filterOutletList, totalOutlet);
        }
        int taskItemId = request.getItemId() != null ? request.getItemId() : splitTaskItemSowWall.getTaskItemId();
        sortCommonBuildService.buildScanResponse(response, taskItemId, splitTaskItemSowWall.getScanQty(), outlet);
        if (splitTaskItemSowWall.getOutlet() == null || splitTaskItemSowWall.getOutlet() == 0) {
            updateItemOutlet(splitTaskItemSowWall.getTaskId(), splitTaskItemSowWall.getStockoutOrderNo(), outlet);
        }
    }

    private void updateItemOutlet(Integer taskId, String stockoutOrderNo, int outlet) {
        List<StockoutBatchSplitTaskItemEntity> splitTaskItemList = splitItemService.getByTaskIdOrderByTaskIdAndStockoutOrderNo(taskId, stockoutOrderNo);
        if (!CollectionUtils.isEmpty(splitTaskItemList)) {
            List<StockoutBatchSplitTaskItemEntity> filterSplitTaskItemList = splitTaskItemList.stream().filter(item -> item.getOutlet() == null || item.getOutlet() == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterSplitTaskItemList)) {
                filterSplitTaskItemList.forEach(splitTaskItem -> {
                    splitTaskItem.setOutlet(outlet);
                    splitTaskItem.setActualOutlet(outlet);
                    splitTaskItem.setUpdateBy(loginInfoService.getName());
                });
                splitItemService.updateBatchById(filterSplitTaskItemList);
            }
        }
    }

    public void checkOutlet(int originOutletQty, Integer totalOutlet) {
        if (originOutletQty == totalOutlet) // 所有分拣口都占满
            throw new BusinessServiceException("当前箱号已排满");
    }

    public Integer buildOutlet(List<Integer> originOutletList, Integer totalOutlet) {
        List<Integer> allOutletList = new ArrayList<>(totalOutlet);
        for (int i = 1; i <= totalOutlet; i++)
            allOutletList.add(i);
        allOutletList.removeAll(originOutletList);
        if (CollectionUtils.isEmpty(allOutletList))
            throw new BusinessServiceException("找不到分拣口");
        else
            return allOutletList.get(0);
    }

    // 该订单号下所有sku已撤货，修改出库单状态取消中为已取消,// 释放分拣口
    void stockoutOrderCancel(Integer taskId, String stockoutOrderNo) {
        List<StockoutBatchSplitTaskItemEntity> splitTaskItemList = splitItemService.getByTaskIdOrderByTaskIdAndStockoutOrderNo(taskId, stockoutOrderNo);
        List<StockoutBatchSplitTaskItemEntity> filterSplitTaskItemList = splitTaskItemList.stream().filter(m -> m.getBatchQty().compareTo(m.getWithdrawalQty()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterSplitTaskItemList)) {
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
            splitItemService.releaseOutlet(taskId, stockoutOrderNo);
            //若是调拨出库或清仓出库，更新调拨单状态
            if (StockoutOrderTypeEnum.TRANSFER_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())
                    || StockoutOrderTypeEnum.CLEAR_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())) {
                stockTransferCrossSpaceService.cancelTransferOut(stockoutOrderEntity.getStockoutOrderNo());
            }
            stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.CANCELLED.name());
            int withdrawalQty = splitTaskItemList.stream().mapToInt(StockoutBatchSplitTaskItemEntity::getWithdrawalQty).sum();
            String orderContent = String.format("出库单【%s】分拣撤货总件数 %s件,状态变为已取消", stockoutOrderNo, withdrawalQty);
            stockoutOrderLogService.addLog(stockoutOrderNo, StockoutOrderLogTypeEnum.SORTING_WITHDRAWAL, orderContent);
        }
    }

    public Integer getTotalOutlet(String secondSortingType) { // 获取分拣口总数
        BdSystemParameterEntity bdSystemParameterEntity;
        if (secondSortingType.equals(BdSystemParameterEnum.WMS_STOCKS_ARTIFICIAL_SEEDING_NUM.getKey())) { // 人工播种墙数
            bdSystemParameterEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_STOCKS_ARTIFICIAL_SEEDING_NUM.getKey());
            if (bdSystemParameterEntity == null || bdSystemParameterEntity.getConfigValue() == null)
                throw new BusinessServiceException(String.format("请先配置%s", BdSystemParameterEnum.WMS_STOCKS_ARTIFICIAL_SEEDING_NUM.getName()));
        } else if (secondSortingType.equals(BdSystemParameterEnum.WMS_STOCKS_LIGHT_SEEDING_NUM.getKey())) { // 亮灯播种墙数
            bdSystemParameterEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_STOCKS_LIGHT_SEEDING_NUM.getKey());
            if (bdSystemParameterEntity == null || bdSystemParameterEntity.getConfigValue() == null)
                throw new BusinessServiceException(String.format("请先配置%s", BdSystemParameterEnum.WMS_STOCKS_LIGHT_SEEDING_NUM.getName()));
        } else
            throw new BusinessServiceException("找不到播种墙数量");
        String maxOutlet = bdSystemParameterEntity.getConfigValue();
        return Integer.valueOf(maxOutlet);
    }

    // 更新分拣明细撤货数
    public void updateSplitTaskItemWithdrawalQty(Integer taskId, String stockoutOrderNo, String sku, Integer scanQty) {
        StockoutBatchSplitTaskItemEntity splitTaskItemEntity = splitItemService.getByTaskIdAndStockoutOrderNoAndSku(taskId, stockoutOrderNo, sku);
        if (Objects.isNull(splitTaskItemEntity))
            throw new BusinessServiceException("分拣撤货找不到分拣任务明细信息");
        int originWithdrawalQty = splitTaskItemEntity.getWithdrawalQty();
        splitTaskItemEntity.setWithdrawalQty(originWithdrawalQty + scanQty);
        splitTaskItemEntity.setUpdateBy(loginInfoService.getName());
        splitItemService.updateById(splitTaskItemEntity);
    }


    // 分拣任务完成分拣：
    void completeBatchSplitTask(Integer taskId, List<StockoutBatchSplitTaskItemEntity> splitTaskItemEntityList) {
        StockoutBatchSplitTaskEntity splitTaskEntity = splitTaskService.getBatchSplitTaskById(taskId);
        splitTaskEntity.setStatus(StockoutBatchSplitTaskTypeEnum.SORTED.name());
        splitTaskEntity.setUpdateBy(loginInfoService.getName());
        splitTaskEntity.setPickingBoxQty(0);
        splitTaskEntity.setOperateEndDate(new Date());
        splitTaskEntity.setOperator(loginInfoService.getName());
        splitTaskService.updateById(splitTaskEntity);
        // StockoutBatchEntity batchEntity = stockoutBatchService.getStockoutBatchById(splitTaskEntity.getBatchId());
        // 分拣任务日志
        Integer totalBatchQty = splitTaskItemEntityList.stream().mapToInt(StockoutBatchSplitTaskItemEntity::getBatchQty).sum();
        Integer totalScanQty = splitTaskItemEntityList.stream().filter(item -> item.getScanQty() != null).mapToInt(StockoutBatchSplitTaskItemEntity::getScanQty).sum();
        Integer totalLackQty = splitTaskItemEntityList.stream().filter(item -> item.getLackQty() != null).mapToInt(StockoutBatchSplitTaskItemEntity::getLackQty).sum();
        int stockoutOrderQty = (int) splitTaskItemEntityList.stream().map(StockoutBatchSplitTaskItemEntity::getStockoutOrderNo).distinct().count();
        String content = String.format("分拣完成, 需分拣%s件，共分拣%s件, 缺货 %s 件, 分拣%s单/波次", totalBatchQty, totalScanQty, totalLackQty, stockoutOrderQty);
        splitTaskService.addSplitTaskLog(taskId, StockoutBatchSplitTaskTypeEnum.SORTED.getName(), content);
        stockoutLogQueueService.addLogQueueOfBatchId(splitTaskEntity.getBatchId(), "分拣完成");
        // 工作区域=B2B和内贸，二次分拣确定缺货 拣货单还不能完成
        /*if (!StockoutDeliveryNoticeEnum.NORMAL_DELIVERY.name().equals(deliveryNoticeType)
                && !(StockoutPickingTypeEnum.SECOND_SORT.name().equals(batchEntity.getPickingType()) && (StockoutOrderWorkSpaceEnum.B2B_AREA.name().equals(batchEntity.getWorkspace())
                || StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name().equals(batchEntity.getWorkspace())))) {
            Map<String, List<StockoutBatchSplitTaskItemEntity>> itemMaps = splitTaskItemEntityList.stream().collect(Collectors.groupingBy(StockoutBatchSplitTaskItemEntity::getStockoutOrderNo));
            itemMaps.forEach((stockoutOrderNo, stockoutBatchSplitTaskItemEntities) -> erpApiInternalService.finishPartialPick(stockoutOrderNo, stockoutBatchSplitTaskItemEntities));
        }*/
    }

    // 缺货亮灯，刷新二次分拣： 返回sku列表数据(缺货数由大到小)、分拣口列表
    public StockoutBatchSplitTaskRefreshResponse getLackOutlet(Integer batchId) {
        StockoutBatchSplitTaskRefreshResponse response = new StockoutBatchSplitTaskRefreshResponse();
        List<StockoutBatchSplitTaskItemSowWall> sorteList = stockoutBatchSplitSecondaryService.refreshSkuList(batchId);
        if (!CollectionUtils.isEmpty(sorteList)) {
            List<StockoutBatchSplitTaskItemSowWall> filterList = sorteList.stream().filter(item -> !item.getBatchQty().equals(item.getScanQty()) && item.getOutlet() != null && item.getOutlet() != 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterList)) {
                List<StockoutBatchSplitTaskItemSowWall> newList = filterList.stream()
                        .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StockoutBatchSplitTaskItemSowWall::getStockoutOrderNo))), ArrayList::new));
                List<StockoutBatchSplitTaskRefreshResponse.Outlet> outletList = new ArrayList<>();
                newList.forEach(item -> {
                    StockoutBatchSplitTaskRefreshResponse.Outlet outlet = new StockoutBatchSplitTaskRefreshResponse.Outlet();
                    outlet.setOutlet(item.getOutlet());
                    outlet.setOutletColor(COLOR_YELLOW);
                    outletList.add(outlet);
                });
                response.setOutletList(outletList);
            } else {
                response.setOutletList(new ArrayList<>());
            }
            response.setStockoutBatchSplitTaskItemSowWallList(sorteList);
        } else {
            response.setStockoutBatchSplitTaskItemSowWallList(new ArrayList<>());
            response.setOutletList(new ArrayList<>());
        }
        return response;
    }

    /**
     * 点击完成扫描按钮相关进行的相关操作
     * 1.校验分拣任务，分拣明细，对应出库单是否存在
     * 2.过滤掉取消中，已取消的出库单对应的分拣明细
     * 3.对于取消中，已取消的出库单对应的分拣明细进行拣货箱库存扣减，调入撤货箱
     * 4.校验分拣明细是否都是已扫描的(或已通知缺货的)
     * 5.变更分拣任务状态分拣完成、波次状态完成
     * 6.(1)无复核任务：生成装箱清单,出库单改待发货，同步net拣货单状态，nep装箱清单；
     * (2)有复核任务，更新待复核数，出库单状态改出库中
     */
    @Transactional
    @JLock(keyConstant = "StockoutBatchSecondarySortService.completeScan", lockKey = "#batchId")
    public StockoutBatchSplitTaskCompleteScanResponse completeScan(Integer batchId) {
        StockoutBatchEntity batchEntity = stockoutBatchService.getStockoutBatchById(batchId);
        if (StockoutWavePlanTypeEnum.LACK_WAVE.name().equals(batchEntity.getBatchType()))
            return stockoutBatchSplitCommonService.lackBatchSplitCompleteScan(batchEntity);
        StockoutBatchSplitTaskCompleteScanResponse response = new StockoutBatchSplitTaskCompleteScanResponse();
        StockoutBatchSplitTaskEntity splitTaskEntity = splitTaskService.getOne(new QueryWrapper<StockoutBatchSplitTaskEntity>().lambda().eq(StockoutBatchSplitTaskEntity::getBatchId, batchId));
        if (Objects.isNull(splitTaskEntity)) throw new BusinessServiceException("找不到分拣任务信息");
        List<StockoutBatchSplitTaskItemEntity> splitTaskItemEntityList = splitItemService.getByTaskIdOrderByTaskId(splitTaskEntity.getTaskId());
        if (CollectionUtils.isEmpty(splitTaskItemEntityList)) throw new BusinessServiceException("找不到分拣任务明细信息");
        List<String> stockoutOrderNoList = splitTaskItemEntityList.stream().map(StockoutBatchSplitTaskItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
        List<StockoutOrderEntity> stockoutOrderEntityList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(stockoutOrderEntityList)) throw new BusinessServiceException("找不到分拣对应出库单信息");
        Map<String, List<StockoutBatchSplitTaskItemEntity>> mapGroup = splitTaskItemEntityList.stream().collect(Collectors.groupingBy(StockoutBatchSplitTaskItemEntity::getStockoutOrderNo));
        List<StockoutBatchSplitTaskItemEntity> itemEntityList = new ArrayList<>();
        Integer mergeBatchId = batchId;
        if (batchEntity.getMergeBatchId() != null && batchEntity.getIsNeedProcess().equals(0))
            mergeBatchId = batchEntity.getMergeBatchId();
        Integer finalBatchId = mergeBatchId;
        mapGroup.forEach((stockoutOrderNo, list) -> {
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderEntityList.stream().filter(order -> stockoutOrderNo.equals(order.getStockoutOrderNo()))
                    .findFirst().orElseThrow(() -> new BusinessServiceException(String.format("分拣明细不存出库单号【%s】", stockoutOrderNo)));
            if (!StockoutOrderStatusEnum.CANCELLED.name().equals(stockoutOrderEntity.getStatus()))
                itemEntityList.addAll(list);
            // 取消中的拣货箱库存扣减，撤货箱增加
            if (StockoutOrderStatusEnum.CANCELLING.name().equals(stockoutOrderEntity.getStatus())) {
                buildCancellingStockoutOrderChangeStock(finalBatchId, list, stockoutOrderEntity.getStockoutOrderId());
                stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.CANCELLED.name());
                String orderContent = String.format("出库单【%s】在波次扫描/复核中, 取消成功", stockoutOrderNo);
                stockoutOrderLogService.addLog(stockoutOrderNo, StockoutOrderLogTypeEnum.COMPLETE_SCAN, orderContent);
            }
        });
        if (CollectionUtils.isEmpty(itemEntityList)) return response;
        List<StockoutBatchSplitTaskItemEntity> filterItemList = itemEntityList.stream().filter(item ->
                item.getBatchQty().compareTo(item.getScanQty()) > 0 && item.getLackQty() == 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterItemList)) {
            LOGGER.error("点击了完成扫描，检查到当前波次: {} 含有未扫描的SKU，若无货请先通知缺货!", batchEntity.getBatchId());
            response.setIsCompleteScan(0);
            response.setMessage("当前波次含有未扫描的SKU，若无货请先通知缺货!");
        } else {
            // 变更分拣任务状态分拣完成、波次状态分拣完成
            LOGGER.info("点击了完成扫描，当前波次: {} 可以强制完成扫描，准备进行相关操作=----------------------------", batchEntity.getBatchId());
            splitTaskService.updateStockoutBatchSplitTaskStatus(splitTaskEntity.getTaskId(), StockoutBatchSplitTaskStatus.SORTED.name());
            stockoutBatchService.updateBatchStatus(batchEntity, StockoutWaveTaskStatusEnum.COMPLETED);

            StockoutBatchScanTypeInfo scanTypeInfo = scanTypeService.getScanTypeInfo(batchEntity);
            // 无复核任务情况  归还波次下剩余的拣货箱库存
            if (!(Objects.nonNull(scanTypeInfo) && scanTypeInfo.getCreateCheckTask() != null && scanTypeInfo.getCreateCheckTask())) {
                stockoutPickingTaskItemService.returnPickingBoxQtyToPosition(batchId);
            }
            sortCommonService.completeSplitTaskLogRecord(splitTaskEntity.getTaskId(), splitTaskItemEntityList);
            stockoutOrderEntityList.forEach(stockoutOrderEntity -> {
                sortCommonService.completeStockoutOrderSplitTask(batchEntity, stockoutOrderEntity);
                //生成简易复核任务
                stockoutEasyScanTaskService.createEasyScanTask(stockoutOrderEntity.getStockoutOrderNo());
            });

            response.setIsCompleteScan(1);
        }
        return response;
    }

    // 取消中的出库单，扣减拣货箱数量，库存变动，放入撤货箱
    private void buildCancellingStockoutOrderChangeStock(Integer batchId, List<StockoutBatchSplitTaskItemEntity> list, Integer stockoutOrderId) {
        StockoutBatchEntity batchEntity = stockoutBatchService.getById(batchId);
        List<StockInternalBoxItemEntity> boxItemList = stockInternalBoxItemService.getByBatchIdAndInternalBoxType(batchId, StockInternalBoxTypeEnum.PICKING_BOX.name());
        Map<Integer, List<StockoutBatchSplitTaskItemEntity>> map = list.stream().collect(Collectors.groupingBy(StockoutBatchSplitTaskItemEntity::getSpecId));
        Set<Integer> boxIdSet = new HashSet<>();
        //撤货箱
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getWithdrawalByWorkspace(batchEntity.getWorkspace(), batchEntity.getSpaceId());
        map.forEach((specId, itemList) -> {
            List<StockInternalBoxItemEntity> filterBoxItemList = boxItemList.stream().filter(m -> m.getSpecId().equals(specId) && m.getQty() > 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterBoxItemList)) {
                int boxQty = filterBoxItemList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
                int sum = itemList.stream().mapToInt(item -> item.getPickedQty() - item.getScanQty()).sum();
                if (sum <= 0)
                    return;
                int minusQty = Math.min(boxQty, sum);
                // 拣货箱数量及库存变动
                StockoutBatchSplitTaskMinusPickingStockBo bo = new StockoutBatchSplitTaskMinusPickingStockBo.Builder().setBatchId(batchId).setStockoutOrderId(stockoutOrderId)
                        .setSpecId(itemList.get(0).getSpecId()).setTotalQty(minusQty).setIsProcess(0).setDeliveryQty(0).build();
                List<StockInternalBoxItemSourcePositionBo> sourcePositionBoList = splitItemService.minusPickingStock(bo);
                // 放入撤货箱中
                sortCommonService.saveOrUpdateWithdrawalBox(stockInternalBoxEntity, productSpecInfoService.findTopBySpecId(specId), batchId, minusQty, sourcePositionBoList);
                List<Integer> internalBoxIdList = filterBoxItemList.stream().map(StockInternalBoxItemEntity::getInternalBoxId).distinct().collect(Collectors.toList());
                boxIdSet.addAll(internalBoxIdList);
            }
        });
        // 内部箱置空
        if (!CollectionUtils.isEmpty(boxIdSet)) {
            List<Integer> boxIdList = new ArrayList<>(boxIdSet);
            List<StockInternalBoxEntity> boxEntityList = stockInternalBoxService.listByIds(boxIdList);
            if (!CollectionUtils.isEmpty(boxEntityList)) {
                boxEntityList.forEach(item -> {
                    item.setStatus(StockInternalBoxStatusEnum.EMPTY.name());
                    item.setUpdateBy(loginInfoService.getName());
                });
                stockInternalBoxService.updateBatchById(boxEntityList);
            }
        }
    }

    // 查出库单已存在的分拣口
    public int getStockoutOrderOutlet(String stockoutOrderNo, List<StockoutBatchSplitTaskItemSowWall> filterList) {
        List<StockoutBatchSplitTaskItemSowWall> list = filterList.stream().filter(item -> stockoutOrderNo.equals(item.getStockoutOrderNo())
                && item.getOutlet() != null && item.getOutlet() != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        } else
            return list.stream().map(StockoutBatchSplitTaskItemSowWall::getOutlet).findFirst().orElse(0);
    }
}
