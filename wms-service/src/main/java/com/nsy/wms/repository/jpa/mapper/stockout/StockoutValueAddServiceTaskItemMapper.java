package com.nsy.wms.repository.jpa.mapper.stockout;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.stockout.StockoutVasTaskItemList;
import com.nsy.api.wms.request.stockout.StockoutVasTaskItemListRequest;
import com.nsy.wms.repository.entity.stockout.StockoutValueAddServiceTaskItemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StockoutValueAddServiceTaskItemMapper extends BaseMapper<StockoutValueAddServiceTaskItemEntity> {
    IPage<StockoutVasTaskItemList> pageSearchList(IPage page, @Param("query") StockoutVasTaskItemListRequest request);
}
