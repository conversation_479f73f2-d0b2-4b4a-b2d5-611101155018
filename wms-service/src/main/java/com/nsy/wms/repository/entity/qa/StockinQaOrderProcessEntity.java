
package com.nsy.wms.repository.entity.qa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 质检单流程表
 *
 * <AUTHOR>
 * @Date 2024-11-18 15:58
 */
@Entity
@TableName("stockin_qa_order_process")
@Table(name = "stockin_qa_order_process")
public class StockinQaOrderProcessEntity extends BaseMpEntity {


    /**
     * StockinQaOrderProcessId
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer stockinQaOrderProcessId;
    /**
     * 地区
     */
    private String location;
    /**
     * 质检单ID
     */
    private Integer stockinQaOrderId;
    /**
     * 质检流程名
     */
    private String processName;
    /**
     * 状态
     */
    private String status;

    /**
     * 质检件数 -- 稽查使用
     */
    private Integer qaCount;
    /**
     * 质检不合格件数
     */
    private Integer unqualifiedCount;
    /**
     * 直接退货件数
     */
    private Integer directReturnCount;
    /**
     * 退货数量
     */
    private Integer returnCount;
    /**
     * 让步接收数
     */
    private Integer concessionsCount;
    
    /**
     * 稽查件数
     */
    private Integer inspectCount;
    /**
     * 备注
     */
    private String remark;

    private String attachmentUrl;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 操作时间
     */
    private Date operateDate;

    private String operator;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getStockinQaOrderProcessId() {
        return stockinQaOrderProcessId;
    }

    public void setStockinQaOrderProcessId(Integer stockinQaOrderProcessId) {
        this.stockinQaOrderProcessId = stockinQaOrderProcessId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getDirectReturnCount() {
        return directReturnCount;
    }

    public void setDirectReturnCount(Integer directReturnCount) {
        this.directReturnCount = directReturnCount;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public Integer getQaCount() {
        return qaCount;
    }

    public void setQaCount(Integer qaCount) {
        this.qaCount = qaCount;
    }

    public Integer getInspectCount() {
        return inspectCount;
    }

    public void setInspectCount(Integer inspectCount) {
        this.inspectCount = inspectCount;
    }
}
