
package com.nsy.wms.repository.entity.qa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 质检单
 *
 * <AUTHOR>
 * @Date 2024-11-18 15:58
 */
@Entity
@TableName("stockin_qa_order")
@Table(name = "stockin_qa_order")
public class StockinQaOrderEntity extends BaseMpEntity {

    /**
     * StockinQaOrderId
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer stockinQaOrderId;
    /**
     * 地区
     */
    private String location;
    /**
     * 质检任务Id
     */
    private Integer taskId;
    /**
     * 内部箱号
     */
    private String internalBoxCode;

    /**
     * 仓库id
     */
    private Integer spaceId;
    /**
     * 商品id
     */
    private Integer productId;
    /**
     * spu
     */
    private String spu;
    /**
     * skc
     */
    private String skc;
    /**
     * 规格编码
     */
    private String sku;
    //业务sku
    private String sellerSku;
    //业务条码
    private String sellerBarcode;
    /**
     * 供应商Id
     */
    private Integer supplierId;
    /**
     * 供应商
     */
    private String supplierName;
    /**
     * 采购员编码
     */
    private String purchaseUserCode;
    /**
     * 采购员名字
     */
    private String purchaseUserName;
    /**
     * 质检员编码
     */
    private String qcUserCode;
    /**
     * 质检员名字
     */
    private String qcUserName;
    /**
     * 质检流程状态
     */
    private String processStatus;
    /**
     * 稽查状态
     */
    private String inspectStatus;
    /**
     * 质检最终结果
     */
    private String result;
    /**
     * 稽查结果
     */
    private String inspectResult;
    /**
     * 质检完成时间
     */
    private Date completeDate;
    /**
     * 稽查完成时间
     */
    private Date inspectCompleteDate;

    /**
     * 仓库名
     */
    private String spaceName;
    /**
     * 不合格原因归类
     */
    private String unqualifiedCategory;
    /**
     * 部门
     */
    private String department;
    /**
     * 申请部门
     */
    private String applyDepartment;
    /**
     * 入库类型
     */
    private String stockinType;
    /**
     * 指派人
     */
    private String appointor;

    /**
     * 用户id -- 做权限过滤
     */
    private Integer userId;

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getPurchaseUserCode() {
        return purchaseUserCode;
    }

    public void setPurchaseUserCode(String purchaseUserCode) {
        this.purchaseUserCode = purchaseUserCode;
    }

    public String getPurchaseUserName() {
        return purchaseUserName;
    }

    public void setPurchaseUserName(String purchaseUserName) {
        this.purchaseUserName = purchaseUserName;
    }

    public String getQcUserCode() {
        return qcUserCode;
    }

    public void setQcUserCode(String qcUserCode) {
        this.qcUserCode = qcUserCode;
    }

    public String getQcUserName() {
        return qcUserName;
    }

    public void setQcUserName(String qcUserName) {
        this.qcUserName = qcUserName;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getApplyDepartment() {
        return applyDepartment;
    }

    public void setApplyDepartment(String applyDepartment) {
        this.applyDepartment = applyDepartment;
    }

    public String getStockinType() {
        return stockinType;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public String getAppointor() {
        return appointor;
    }

    public void setAppointor(String appointor) {
        this.appointor = appointor;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getSellerBarcode() {
        return sellerBarcode;
    }

    public void setSellerBarcode(String sellerBarcode) {
        this.sellerBarcode = sellerBarcode;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getInspectStatus() {
        return inspectStatus;
    }

    public void setInspectStatus(String inspectStatus) {
        this.inspectStatus = inspectStatus;
    }

    public Date getInspectCompleteDate() {
        return inspectCompleteDate;
    }

    public void setInspectCompleteDate(Date inspectCompleteDate) {
        this.inspectCompleteDate = inspectCompleteDate;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getInspectResult() {
        return inspectResult;
    }

    public void setInspectResult(String inspectResult) {
        this.inspectResult = inspectResult;
    }
}
