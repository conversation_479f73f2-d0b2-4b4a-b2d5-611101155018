
package com.nsy.wms.repository.entity.qa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 质检单子表
 *
 * <AUTHOR>
 * @Date 2024-11-18 15:58
 */
@Entity
@TableName("stockin_qa_order_detail")
@Table(name = "stockin_qa_order_detail")
public class StockinQaOrderDetailEntity extends BaseMpEntity {


    /**
     * StockinQaOrderDetailId
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer stockinQaOrderDetailId;
    /**
     * 地区
     */
    private String location;
    /**
     * 质检单ID
     */
    private Integer stockinQaOrderId;
    /**
     * 到货数量
     */
    private Integer arrivalCount;
    /**
     * 箱内数
     */
    private Integer boxQty;
    /**
     * 质检数量
     */
    private Integer testTotalCount;

    /**
     * 稽查数量
     */
    private Integer inspectTotalCount;

    /**
     * 质检不合格件数
     */
    private Integer unqualifiedCount;
    /**
     * 直接退货件数
     */
    private Integer directReturnCount;
    /**
     * 退货数量
     */
    private Integer returnCount;
    /**
     * 让步接收数
     */
    private Integer concessionsCount;
    /**
     * 轻微缺陷数量
     */
    private Integer minorDefectCount;

    /**
     * 严重缺陷数量
     */
    private Integer majorDefectCount;

    /**
     * 致命缺陷数量
     */
    private Integer criticalDefectCount;
    
    /**
     * 责任方
     */
    private String responsibility;
    /**
     * 责任备注
     */
    private String responsibilityRemark;

    //责任部门
    private String departResponsibility;
    /**
     * 处理方案
     */
    private String processingProgram;
    /**
     * 附件url
     */
    private String attachmentUrl;
    /**
     * 工艺版本号
     */
    private String workmanshipVersion;
    /**
     * 发货时间
     */
    private Date deliveryDate;
    /**
     * 入库时间
     */
    private Date stockinDate;

    /**
     * 稽查时间
     */
    private Date qaInspectDate;

    public Date getQaInspectDate() {
        return qaInspectDate;
    }

    public void setQaInspectDate(Date qaInspectDate) {
        this.qaInspectDate = qaInspectDate;
    }

    public String getResponsibilityRemark() {
        return responsibilityRemark;
    }

    public void setResponsibilityRemark(String responsibilityRemark) {
        this.responsibilityRemark = responsibilityRemark;
    }

    public String getDepartResponsibility() {
        return departResponsibility;
    }

    public void setDepartResponsibility(String departResponsibility) {
        this.departResponsibility = departResponsibility;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer stockinQty) {
        this.boxQty = stockinQty;
    }

    public Integer getStockinQaOrderDetailId() {
        return stockinQaOrderDetailId;
    }

    public void setStockinQaOrderDetailId(Integer stockinQaOrderDetailId) {
        this.stockinQaOrderDetailId = stockinQaOrderDetailId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getTestTotalCount() {
        return testTotalCount;
    }

    public void setTestTotalCount(Integer testTotalCount) {
        this.testTotalCount = testTotalCount;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getDirectReturnCount() {
        return directReturnCount;
    }

    public void setDirectReturnCount(Integer directReturnCount) {
        this.directReturnCount = directReturnCount;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public String getResponsibility() {
        return responsibility;
    }

    public void setResponsibility(String responsibility) {
        this.responsibility = responsibility;
    }

    public String getProcessingProgram() {
        return processingProgram;
    }

    public void setProcessingProgram(String processingProgram) {
        this.processingProgram = processingProgram;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getWorkmanshipVersion() {
        return workmanshipVersion;
    }

    public void setWorkmanshipVersion(String workmanshipVersion) {
        this.workmanshipVersion = workmanshipVersion;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public Integer getInspectTotalCount() {
        return inspectTotalCount;
    }

    public void setInspectTotalCount(Integer inspectTotalCount) {
        this.inspectTotalCount = inspectTotalCount;
    }

    public Integer getMinorDefectCount() {
        return minorDefectCount;
    }

    public void setMinorDefectCount(Integer minorDefectCount) {
        this.minorDefectCount = minorDefectCount;
    }

    public Integer getMajorDefectCount() {
        return majorDefectCount;
    }

    public void setMajorDefectCount(Integer majorDefectCount) {
        this.majorDefectCount = majorDefectCount;
    }

    public Integer getCriticalDefectCount() {
        return criticalDefectCount;
    }

    public void setCriticalDefectCount(Integer criticalDefectCount) {
        this.criticalDefectCount = criticalDefectCount;
    }
}
