package com.nsy.wms.controller.bd;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.request.bd.BdStockinAutoShelveRuleAddRequest;
import com.nsy.api.wms.request.bd.BdStockinAutoShelveRuleListRequest;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.response.bd.BdStockinAutoShelveRuleListResponse;
import com.nsy.wms.business.service.bd.BdStockinAutoShelveRuleService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @author: ca<PERSON><PERSON><PERSON>
 * @version: v1.0
 * @description: 自动上架规则Controller
 * @date: 2024-03-21 10:39
 */
@Api(tags = "自动上架规则")
@RestController
@RequestMapping("/bd-stockin-auto-shelve-rule")
public class BdStockinAutoShelveRuleController extends BaseController {

    @Autowired
    private BdStockinAutoShelveRuleService autoShelveRuleService;

    @ApiOperation(value = "新增规则", produces = "application/json")
    @PostMapping("/add/rule")
    public void addRule(@Valid @RequestBody BdStockinAutoShelveRuleAddRequest request) {
        autoShelveRuleService.addRule(request);
    }

    @ApiOperation(value = "修改规则", produces = "application/json")
    @PostMapping("/edit/rule")
    public void editRule(@Valid @RequestBody BdStockinAutoShelveRuleAddRequest request) {
        autoShelveRuleService.editRule(request);
    }

    @ApiOperation(value = "规则列表", produces = "application/json")
    @PostMapping("/page/list")
    public PageResponse<BdStockinAutoShelveRuleListResponse> getRuleList(@RequestBody BdStockinAutoShelveRuleListRequest request) {
        return autoShelveRuleService.getRuleList(request);
    }

    @ApiOperation(value = "批量删除规则", produces = "application/json")
    @PostMapping("/batch/remove-rule")
    public void batchDelete(@RequestBody IdListRequest request) {
        autoShelveRuleService.batchDelete(request.getIdList());
    }
} 