package com.nsy.wms.service.bd;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.wms.SpringServiceTest;
import com.nsy.api.wms.domain.bd.BdHsCodeModel;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.wms.repository.entity.bd.BdHsCodeEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdHsCodeMapper;
import com.nsy.wms.business.service.bd.BdHsCodeService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * HXD
 * 2021/7/26
 **/

public class BdHsCodeServiceTest extends SpringServiceTest {
    @Autowired
    BdHsCodeService bdHsCodeService;
    @Autowired
    BdHsCodeMapper bdHsCodeMapper;

    @BeforeEach
    public void init() {
        BdHsCodeEntity entity = generateEntity();
        bdHsCodeService.save(entity);
    }

    private BdHsCodeEntity generateEntity() {
        BdHsCodeEntity entity = new BdHsCodeEntity();
        entity.setHsCode("codeTest");
        entity.setHsCodeCn("中文描述");
        entity.setUnit("035/011");
        entity.setUnitCn("千克/条");
        return entity;
    }


    @AfterEach
    public void deleteTestData() {
        bdHsCodeMapper.delete(new QueryWrapper<>());
    }


    @Test
    public void getList() {
        List<SelectModel> selectModelList = bdHsCodeService.hsCodeSelectModelList();
        Assertions.assertThat(selectModelList.size()).isGreaterThan(0);
    }

    @Test
    public void getOneByHsCode() {
        BdHsCodeModel codeTest = bdHsCodeService.getByHsCode("codeTest");
        Assertions.assertThat(codeTest).isNotNull();
        Assertions.assertThat(codeTest.getHsCodeCn()).isEqualTo("中文描述");
    }
}
